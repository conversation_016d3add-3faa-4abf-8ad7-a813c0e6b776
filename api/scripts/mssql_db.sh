#!/bin/bash

ENV=/home/<USER>/apps/.env
# Load environment variables from .env file
if [ -f "$ENV" ]; then
    export $(grep -v '^#' $ENV | xargs)
fi

connectDB() {
	echo "Connecting to database..."
    docker exec -it mssql /opt/mssql-tools18/bin/sqlcmd \
        -S localhost \
        -U SA \
        -P "$DB_PASSWORD" \
        -C \
        -d SocialStockDB
}

dropTables() {
	echo "Dropping all tables..."

	# Drop tables in correct order (considering foreign key constraints)
	echo "Dropping tables with foreign key dependencies first..."
	docker exec -it mssql /opt/mssql-tools18/bin/sqlcmd \
	    -S localhost \
	    -U SA \
	    -P "$DB_PASSWORD" \
	    -C \
	    -d SocialStockDB \
	    -Q "
	-- Drop tables with foreign key dependencies first
	DROP TABLE IF EXISTS Comments;
	DROP TABLE IF EXISTS AspNetUserTokens;
	DROP TABLE IF EXISTS AspNetUserRoles;
	DROP TABLE IF EXISTS AspNetUserLogins;
	DROP TABLE IF EXISTS AspNetUserClaims;
	DROP TABLE IF EXISTS AspNetRoleClaims;
	DROP TABLE IF EXISTS AspNetUsers;
	DROP TABLE IF EXISTS AspNetRoles;
	DROP TABLE IF EXISTS Stocks;
	DROP TABLE IF EXISTS __EFMigrationsHistory;
	"

	echo "All tables dropped successfully!"
}

dropAllTables() {
	echo "Dropping ALL tables dynamically..."

	# First, disable all foreign key constraints
	echo "Disabling foreign key constraints..."
	docker exec -it mssql /opt/mssql-tools18/bin/sqlcmd \
	    -S localhost \
	    -U SA \
	    -P "$DB_PASSWORD" \
	    -C \
	    -d SocialStockDB \
	    -Q "EXEC sp_MSforeachtable 'ALTER TABLE ? NOCHECK CONSTRAINT ALL';"

	# Then drop all tables
	echo "Dropping all tables..."
	docker exec -it mssql /opt/mssql-tools18/bin/sqlcmd \
	    -S localhost \
	    -U SA \
	    -P "$DB_PASSWORD" \
	    -C \
	    -d SocialStockDB \
	    -Q "
	DECLARE @sql NVARCHAR(MAX) = '';
	SELECT @sql = @sql + 'DROP TABLE [' + TABLE_SCHEMA + '].[' + TABLE_NAME + '];' + CHAR(13)
	FROM INFORMATION_SCHEMA.TABLES
	WHERE TABLE_TYPE = 'BASE TABLE'
	AND TABLE_SCHEMA = 'dbo';
	EXEC sp_executesql @sql;
	"

	echo "All tables dropped successfully!"
}

createTables() {
	local migration_name=$1
	if [ -z "$migration_name" ]; then
		echo "Usage: createTables <migration_name>"
		return 1
	fi

	echo "Creating migration: $migration_name"
	dotnet ef migrations add $migration_name

	echo "Creating tables..."
	dotnet ef database update
}

queryDB() {
	local query=$1
	if [ -z "$query" ]; then
		echo "Usage: queryDB \"SQL_QUERY\""
		return 1
	fi

	docker exec -it mssql /opt/mssql-tools18/bin/sqlcmd \
	    -S localhost \
	    -U SA \
	    -P "$DB_PASSWORD" \
	    -C \
	    -d SocialStockDB \
	    -Q "$query"
}

listTables() {
	echo "Listing all tables..."
	queryDB "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';"
}

showStocks() {
	echo "Showing stock data..."
	queryDB "SELECT TOP 10 Id, Symbol, CompanyName, Purchase, Industry FROM Stocks ORDER BY Symbol;"
}

showComments() {
	echo "Showing comments data..."
	queryDB "SELECT TOP 10 c.Id, c.Title, s.Symbol, c.CreatedOn FROM Comments c JOIN Stocks s ON c.StockId = s.Id ORDER BY c.CreatedOn DESC;"
}

containerStatus() {
	echo "MSSQL Container Status:"
	docker ps -f name=mssql
}

# Handle command line arguments
case "$1" in
	"connect")
		connectDB
		;;
	"drop")
		dropTables
		;;
	"dropall")
		dropAllTables
		;;
	"create")
		createTables "$2"
		;;
	"query")
		queryDB "$2"
		;;
	"tables")
		listTables
		;;
	"stocks")
		showStocks
		;;
	"comments")
		showComments
		;;
	"status")
		containerStatus
		;;
	*)
		echo "Usage: $0 {connect|drop|dropall|create <migration_name>|query \"SQL\"|tables|stocks|comments|status}"
		echo ""
		echo "Commands:"
		echo "  connect                     - Connect to database interactively"
		echo "  drop                       - Drop known application tables"
		echo "  dropall                    - Drop ALL tables dynamically"
		echo "  create <migration_name>    - Create migration and update database"
		echo "  query \"SQL\"                - Execute SQL query"
		echo "  tables                     - List all tables"
		echo "  stocks                     - Show stock data"
		echo "  comments                   - Show comments data"
		echo "  status                     - Show container status"
		exit 1
		;;
esac