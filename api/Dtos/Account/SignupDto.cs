using System;
using System.ComponentModel.DataAnnotations;

namespace api.Dtos.Account;

public class SignupDto
{
	[Required]
	public required string Username { get; set; }

	[Required]
	[EmailAddress]
	public required string Email { get; set; }

	[Required]
	[MinLength(8, ErrorMessage = "Password must be at least 8 characters")]
	[DataType(DataType.Password)]
	public required string Password { get; set; }
}
