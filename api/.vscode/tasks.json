{"version": "2.0.0", "tasks": [{"label": "MSSQL: Connect to Database", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["connect"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "detail": "Connect to MSSQL database interactively using sqlcmd"}, {"label": "MSSQL: Drop All Tables", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["drop"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Drop all application tables (Comments, Stocks, __EFMigrationsHistory)"}, {"label": "MSSQL: Drop ALL Tables (Dynamic)", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["dropall"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Dynamically drop ALL tables in the database (including Identity tables)"}, {"label": "MSSQL: List All Tables", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["tables"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "List all tables in the SocialStockDB database"}, {"label": "MSSQL: Show Stock Data", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["stocks"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Display top 10 stock records from the Stocks table"}, {"label": "MSSQL: Show Comments Data", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["comments"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Display top 10 comments with associated stock symbols"}, {"label": "MSSQL: Container Status", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["status"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Show MSSQL Docker container status"}, {"label": "MSSQL: Execute Custom SQL Query", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["query", "${input:sqlQuery}"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Execute a custom SQL query (will prompt for input)"}, {"label": "EF: Create Migration & Update Database", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["create", "${input:migrationName}"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Create a new Entity Framework migration and update the database"}, {"label": "EF: Apply Pending Migrations", "type": "shell", "command": "dotnet", "args": ["ef", "database", "update"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Apply any pending Entity Framework migrations"}, {"label": "EF: List Migrations", "type": "shell", "command": "dotnet", "args": ["ef", "migrations", "list"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "List all Entity Framework migrations"}, {"label": "Quick: Show All Data", "type": "shell", "command": "./scripts/mssql_db.sh", "args": ["query", "SELECT 'Stocks' as TableName, COUNT(*) as RecordCount FROM Stocks UNION ALL SELECT 'Comments' as TableName, COUNT(*) as RecordCount FROM Comments;"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Show record counts for all main tables"}, {"label": "Dev: Reset Database (Drop + Migrate)", "type": "shell", "command": "bash", "args": ["-c", "./scripts/mssql_db.sh drop && dotnet ef database update"], "problemMatcher": [], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "Drop all tables and re-apply migrations (development only)"}], "inputs": [{"id": "migrationName", "description": "Enter migration name", "default": "UpdateModel", "type": "promptString"}, {"id": "sqlQuery", "description": "Enter SQL query to execute", "default": "SELECT TOP 5 * FROM Stocks;", "type": "promptString"}]}