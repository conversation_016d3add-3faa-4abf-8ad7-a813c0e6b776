using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using api.Data;
using api.Interfaces;
using api.Models;

namespace api.Repositories
{
    public class PortfolioRepository : IPortfolioRepository
    {
        private readonly ApplicationDBContext _context;

        public PortfolioRepository(ApplicationDBContext context)
        {
            _context = context;
        }

        public Task<List<Stock>> GetPortfolioAsync(User user)
        {
            return _context.Portfolios
                            .Where(p => p.UserId == user.Id)
                            .Select(p => p.Stock)
                            .ToListAsync();
        }
    }
}