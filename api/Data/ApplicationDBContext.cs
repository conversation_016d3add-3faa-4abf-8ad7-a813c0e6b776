using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using api.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace api.Data
{
  public class ApplicationDBContext : IdentityDbContext<User>
  {
    public ApplicationDBContext(DbContextOptions dbContextOptions) : base(dbContextOptions)
    {
    }
    public DbSet<Stock> Stocks { get; set; }
    public DbSet<Comment> Comments { get; set; }
    protected override void OnModelCreating(ModelBuilder builder)
    {
      base.OnModelCreating(builder);

      builder.Entity<Portfolio>(x => x.<PERSON>ey(p => new { p.UserId, p.StockId }));

      builder.Entity<Portfolio>()
        .HasOne(p => p.User)
        .WithMany(u => u.Portfolios)
        .HasForeignKey(p => p.UserId);

      builder.Entity<Portfolio>()
        .HasOne(p => p.Stock)
        .WithMany(s => s.Portfolios)
        .HasForeignKey(p => p.StockId);

      List<IdentityRole> roles = new List<IdentityRole>
            {
              new IdentityRole {
                Name = "Admin",
                NormalizedName = "ADMIN"
              },
              new IdentityRole {
                Name = "User",
                NormalizedName = "USER"
              }
            };
      builder.Entity<IdentityRole>().HasData(roles);
    }
  }
}